# 手动精修新方案实现说明

## 方案概述

根据您的要求，我们实现了一个全新的手动精修方案，通过分离画笔操作画布和显示画布来解决之前的点位记录、操作卡顿等问题。

## 核心改进

### 1. 双画布架构

**显示画布 (manualRefineCanvas)**
- 用于显示原始图像 + 红色蒙层
- 只负责显示，不处理鼠标事件
- z-index 较低

**操作画布 (redMaskCanvas)**
- 用于画笔操作和红色蒙层绘制
- 处理所有鼠标事件
- z-index 较高，覆盖在显示画布之上
- 背景透明，只显示用户绘制的红色蒙层

### 2. 文件结构修改

#### HTML 结构
```vue
<!-- 手动精修画布 -->
<canvas
  ref="manualRefineCanvas"
  class="manual-refine-canvas"
  :style="canvasStyle"
></canvas>

<!-- 红色蒙层画布 - 用于画笔操作 -->
<canvas
  ref="redMaskCanvas"
  class="red-mask-canvas"
  :style="canvasStyle"
  @mousedown="startCanvasDrawing"
  @mousemove="handleCanvasDrawing"
  @mouseup="stopCanvasDrawing"
  @mouseleave="stopCanvasDrawing"
></canvas>
```

#### CSS 样式
```scss
.red-mask-canvas {
  position: absolute;
  cursor: crosshair;
  background: transparent;
  transform-origin: center center;
  z-index: 2; // 在 manual-refine-canvas 之上
  pointer-events: auto;
}
```

### 3. 核心逻辑改进

#### 初始化流程
1. **双画布初始化** - 同时初始化显示画布和操作画布
2. **尺寸同步** - 确保两个画布尺寸完全一致
3. **抗锯齿设置** - 为操作画布设置高质量图像平滑
4. **画布清空** - 初始化时清空两个画布

#### 画笔绘制流程
1. **事件处理** - 所有鼠标事件由 `redMaskCanvas` 处理
2. **坐标计算** - 基于 `redMaskCanvas` 的边界计算鼠标坐标
3. **红色蒙层绘制** - 在 `redMaskCanvas` 上绘制红色蒙层
4. **实时预览** - 用户可以实时看到绘制效果

#### 图像更新流程
1. **像素数据提取** - 从 `redMaskCanvas` 提取红色蒙层像素数据
2. **基础图像绘制** - 将 `mattingOriginalImage` 绘制到目标画布
3. **颜色还原** - 根据红色蒙层位置从原始文件取色还原
4. **透明处理** - 将擦除区域设置为透明
5. **高质量输出** - 使用抗锯齿和高质量压缩

## 技术实现细节

### 1. 变量定义
```javascript
const manualRefineCanvas = ref<HTMLCanvasElement | null>(null)
const manualRefineCanvasContext = ref<CanvasRenderingContext2D | null>(null)
const redMaskCanvas = ref<HTMLCanvasElement | null>(null)
const redMaskCanvasContext = ref<CanvasRenderingContext2D | null>(null)
```

### 2. 画布初始化
```javascript
// 初始化红色蒙层画布
const redMaskCtx = redMaskCanvas.value.getContext('2d')
redMaskCanvasContext.value = redMaskCtx

// 设置画布的抗锯齿和图像平滑属性
redMaskCtx.imageSmoothingEnabled = true
redMaskCtx.imageSmoothingQuality = 'high'

// 同步画布尺寸
canvas.width = originalImg.width
canvas.height = originalImg.height
redMaskCanvas.value!.width = originalImg.width
redMaskCanvas.value!.height = originalImg.height
```

### 3. 画笔绘制
```javascript
const drawRedMask = (x: number, y: number) => {
  const ctx = redMaskCanvasContext.value
  
  // 根据画笔类型设置颜色和混合模式
  if (batchStore.brushType === 'keep') {
    ctx.globalCompositeOperation = 'source-over'
    ctx.strokeStyle = 'rgba(246, 0, 0, 0.3)'
  } else {
    ctx.globalCompositeOperation = 'destination-out'
    ctx.strokeStyle = 'rgba(0, 0, 0, 1)'
  }
  
  // 绘制连续线条
  ctx.beginPath()
  ctx.moveTo(lastX.value, lastY.value)
  ctx.lineTo(x, y)
  ctx.stroke()
}
```

### 4. 图像更新算法
```javascript
const updateMattingImageFromCanvas = () => {
  // 获取红色蒙层画布的像素数据
  const redMaskData = redMaskCtx.getImageData(0, 0, width, height)
  const redMaskPixels = redMaskData.data
  
  // 创建目标画布并设置抗锯齿
  const targetCtx = targetCanvas.getContext('2d')
  targetCtx.imageSmoothingEnabled = true
  targetCtx.imageSmoothingQuality = 'high'
  
  // 1. 绘制 mattingOriginalImage 作为基础
  targetCtx.drawImage(mattingOriginalImg, 0, 0)
  
  // 2. 根据红色蒙层位置从原始文件取色还原
  for (let i = 0; i < redMaskPixels.length; i += 4) {
    const r = redMaskPixels[i]
    const g = redMaskPixels[i + 1]
    const b = redMaskPixels[i + 2]
    const a = redMaskPixels[i + 3]
    
    // 检查是否有红色蒙层（保留区域）
    if (a > 0 && r > 100 && g < 100 && b < 100) {
      // 从原始文件取色还原
      targetPixels[i] = originalPixels[i]
      targetPixels[i + 1] = originalPixels[i + 1]
      targetPixels[i + 2] = originalPixels[i + 2]
      targetPixels[i + 3] = originalPixels[i + 3]
    }
    // 检查是否是擦除区域
    else if (a === 0) {
      targetPixels[i + 3] = 0 // 设置为透明
    }
  }
}
```

## 优势分析

### 1. 性能优化
- **消除点位记录** - 不再需要复杂的点位插值和冲突检测
- **减少计算开销** - 直接基于像素数据处理，避免复杂算法
- **流畅的绘制体验** - 原生 Canvas 绘制，无卡顿

### 2. 质量提升
- **抗锯齿支持** - 高质量图像平滑设置
- **精确的颜色还原** - 直接从原始文件取色
- **边缘质量** - 避免了红色检测算法的误判

### 3. 架构清晰
- **职责分离** - 显示和操作分离，逻辑清晰
- **易于维护** - 减少了复杂的状态管理
- **扩展性好** - 便于后续功能扩展

## 兼容性保证

- 保持了原有的视觉效果和用户体验
- 画笔类型、大小、柔和度等设置完全兼容
- 不影响其他功能模块的正常运行
- 保留了 `addPortraitRedOverlay` 的羽化功能

## 测试建议

1. **基础功能测试**
   - 保留画笔绘制测试
   - 擦除画笔绘制测试
   - 画笔大小和柔和度调整测试

2. **性能测试**
   - 连续快速绘制测试
   - 大尺寸图像处理测试
   - 长时间使用稳定性测试

3. **质量测试**
   - 边缘平滑度检查
   - 颜色还原准确性测试
   - 不同图像类型兼容性测试

## 后续优化方向

1. **历史记录功能** - 基于画布状态的撤销/重做
2. **性能监控** - 添加性能指标监控
3. **用户体验** - 优化画笔预览和反馈
4. **功能扩展** - 支持更多画笔类型和效果
